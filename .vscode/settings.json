{"breadcrumbs.enabled": false, "debug.internalConsoleOptions": "neverOpen", "debug.showInStatusBar": "never", "editor.acceptSuggestionOnCommitCharacter": false, "editor.acceptSuggestionOnEnter": "off", "editor.autoClosingBrackets": "never", "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "editor.hover.enabled": false, "editor.inlineSuggest.enabled": false, "editor.minimap.enabled": false, "editor.parameterHints.enabled": false, "editor.quickSuggestions": {"other": false, "comments": false, "strings": false}, "editor.referenceInfos": false, "editor.snippetSuggestions": "none", "editor.suggest.statusBar.visible": false, "editor.suggestOnTriggerCharacters": false, "editor.tabSize": 2, "explorer.autoReveal": false, "explorer.openEditors.visible": 0, "extensions.autoCheckUpdates": false, "extensions.ignoreRecommendations": true, "files.autoSave": "after<PERSON>elay", "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, ".vscode": true, ".gitignore": true, ".freeCodeCamp": true, "learn-number-guessing-game": true, ".gitpod.Dockerfile": true, ".gitpod.yml": true, "CHANGELOG.md": true, "coderoad.yaml": true, "tutorial.json": true, "TUTORIAL.md": true}, "html.autoClosingTags": false, "npm.fetchOnlinePackageInfo": false, "task.slowProviderWarning": false, "terminal.integrated.allowChords": false, "terminal.integrated.commandsToSkipShell": ["coderoad.enter"], "terminal.integrated.enableFileLinks": false, "terminal.integrated.environmentChangesIndicator": "off", "terminal.integrated.macOptionIsMeta": true, "terminal.integrated.showExitAlert": false, "telemetry.enableTelemetry": false, "update.mode": "none", "update.showReleaseNotes": false, "workbench.enableExperiments": false, "workbench.startupEditor": "none", "workbench.colorTheme": "Tomorrow Night Blue", "workbench.colorCustomizations": {"[Tomorrow Night Blue]": {"menu.background": "#0a0a23", "menu.foreground": "#ffffff", "activityBar.background": "#0a0a23", "activityBar.foreground": "#ffffff", "activityBar.activeBorder": "#ffffff", "activityBar.border": "#2a2a40", "editorWidget.background": "#0a0a23", "editorWidget.foreground": "#ffffff", "sideBar.background": "#1b1b32", "sideBarTitle.foreground": "#858591", "sideBar.foreground": "#f5f6f7", "sideBar.border": "#2a2a40", "editor.background": "#2a2a40", "editor.foreground": "#dfdfe2", "tab.activeForeground": "#ffffff", "tab.inactiveBackground": "#1b1b32", "tab.inactiveForeground": "#d0d0d5", "tab.border": "#2a2a40", "editorGroupHeader.tabsBackground": "#0a0a23", "editorIndentGuide.background": "#3b3b4f", "terminal.background": "#0a0a23", "terminal.foreground": "#ffffff", "terminal.ansiBrightGreen": "#ffffff", "panel.background": "#1b1b32", "panelTitle.inactiveForeground": "#858591", "panelTitle.activeBorder": "#f5f6f7"}}, "workbench.iconTheme": null, "workbench.statusBar.visible": false, "workbench.tips.enabled": false, "workbench.tree.renderIndentGuides": "none", "zenMode.centerLayout": false}