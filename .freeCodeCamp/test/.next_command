[ -d "$HOME/bin" ]
[ -d "$HOME/.local/bin" ]
PATH="$HOME/.local/bin:$PATH"
[ -e /home/<USER>/.nix-profile/etc/profile.d/nix.sh ]
. /home/<USER>/.nix-profile/etc/profile.d/nix.sh
[[ -s "$HOME/.rvm/scripts/rvm" ]]
source "$HOME/.rvm/scripts/rvm"
[[ -n $SSH_CONNECTION ]]
cd "/workspace/project"
'/home/<USER>/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/node' -p '"6ebe27db8d1c" + JSON.stringify(process.env) + "6ebe27db8d1c"'
[ -d "$HOME/bin" ]
[ -d "$HOME/.local/bin" ]
PATH="$HOME/.local/bin:$PATH"
[ -e /home/<USER>/.nix-profile/etc/profile.d/nix.sh ]
. /home/<USER>/.nix-profile/etc/profile.d/nix.sh
[[ -s "$HOME/.rvm/scripts/rvm" ]]
source "$HOME/.rvm/scripts/rvm"
[[ -n $SSH_CONNECTION ]]
cd "/workspace/project"
echo -n eda52d2f-0c04-4695-bf0e-a0bf66169fe9
cat /proc/self/environ
echo -n eda52d2f-0c04-4695-bf0e-a0bf66169fe9
builtin unset VSCODE_INJECTION
[ -z "$VSCODE_SHELL_INTEGRATION" ]
[ -n "${VSCODE_ENV_REPLACE:-}" ]
[ -n "${VSCODE_ENV_PREPEND:-}" ]
[ -n "${VSCODE_ENV_APPEND:-}" ]
__vsc_regex_environment="^CYGWIN*|MINGW*|MSYS*"
[[ "$(uname -s)" =~ $__vsc_regex_environment ]]
__vsc_is_windows=0
__vsc_regex_histcontrol=".*(erasedups|ignoreboth|ignoredups).*"
[[ "$HISTCONTROL" =~ $__vsc_regex_histcontrol ]]
__vsc_history_verify=1
builtin unset __vsc_regex_environment
builtin unset __vsc_regex_histcontrol
__vsc_initialized=0
__vsc_original_PS1="$PS1"
__vsc_original_PS2="$PS2"
__vsc_custom_PS1=""
__vsc_custom_PS2=""
__vsc_in_command_execution="1"
__vsc_current_command=""
__vsc_nonce="$VSCODE_NONCE"
unset VSCODE_NONCE
__vsc_stable="$VSCODE_STABLE"
unset VSCODE_STABLE
[ "$__vsc_stable" = "0" ]
[ -n "$STARSHIP_SESSION_KEY" ]
[ -n "$POSH_SESSION_ID" ]
builtin printf '\e]633;P;HasRichCommandDetection=True\a'
[[ -n "${bash_preexec_imported:-}" ]]
__vsc_dbg_trap="$(__vsc_get_trap DEBUG)"
[[ -z "$__vsc_dbg_trap" ]]
[[ "$__vsc_dbg_trap" != '__vsc_preexec "$_"' && "$__vsc_dbg_trap" != '__vsc_preexec_all "$_"' ]]
trap '__vsc_preexec_all "$_"' DEBUG
__vsc_original_prompt_command=${PROMPT_COMMAND:-}
builtin unset VSCODE_INJECTION
[ -z "$VSCODE_SHELL_INTEGRATION" ]
[ -n "${VSCODE_ENV_REPLACE:-}" ]
[ -n "${VSCODE_ENV_PREPEND:-}" ]
[ -n "${VSCODE_ENV_APPEND:-}" ]
__vsc_regex_environment="^CYGWIN*|MINGW*|MSYS*"
[[ "$(uname -s)" =~ $__vsc_regex_environment ]]
__vsc_is_windows=0
__vsc_regex_histcontrol=".*(erasedups|ignoreboth|ignoredups).*"
[[ "$HISTCONTROL" =~ $__vsc_regex_histcontrol ]]
__vsc_history_verify=1
builtin unset __vsc_regex_environment
builtin unset __vsc_regex_histcontrol
__vsc_initialized=0
__vsc_original_PS1="$PS1"
__vsc_original_PS2="$PS2"
__vsc_custom_PS1=""
__vsc_custom_PS2=""
__vsc_in_command_execution="1"
__vsc_current_command=""
__vsc_nonce="$VSCODE_NONCE"
unset VSCODE_NONCE
__vsc_stable="$VSCODE_STABLE"
unset VSCODE_STABLE
[ "$__vsc_stable" = "0" ]
[ -n "$STARSHIP_SESSION_KEY" ]
[ -n "$POSH_SESSION_ID" ]
builtin printf '\e]633;P;HasRichCommandDetection=True\a'
[[ -n "${bash_preexec_imported:-}" ]]
__vsc_dbg_trap="$(__vsc_get_trap DEBUG)"
[[ -z "$__vsc_dbg_trap" ]]
[[ "$__vsc_dbg_trap" != '__vsc_preexec "$_"' && "$__vsc_dbg_trap" != '__vsc_preexec_all "$_"' ]]
trap '__vsc_preexec_all "$_"' DEBUG
__vsc_original_prompt_command=${PROMPT_COMMAND:-}
builtin unset VSCODE_INJECTION
[ -z "$VSCODE_SHELL_INTEGRATION" ]
[ -n "${VSCODE_ENV_REPLACE:-}" ]
[ -n "${VSCODE_ENV_PREPEND:-}" ]
[ -n "${VSCODE_ENV_APPEND:-}" ]
__vsc_regex_environment="^CYGWIN*|MINGW*|MSYS*"
[[ "$(uname -s)" =~ $__vsc_regex_environment ]]
__vsc_is_windows=0
__vsc_regex_histcontrol=".*(erasedups|ignoreboth|ignoredups).*"
[[ "$HISTCONTROL" =~ $__vsc_regex_histcontrol ]]
__vsc_history_verify=1
builtin unset __vsc_regex_environment
builtin unset __vsc_regex_histcontrol
__vsc_initialized=0
__vsc_original_PS1="$PS1"
__vsc_original_PS2="$PS2"
__vsc_custom_PS1=""
__vsc_custom_PS2=""
__vsc_in_command_execution="1"
__vsc_current_command=""
__vsc_nonce="$VSCODE_NONCE"
unset VSCODE_NONCE
__vsc_stable="$VSCODE_STABLE"
unset VSCODE_STABLE
[ "$__vsc_stable" = "0" ]
[ -n "$STARSHIP_SESSION_KEY" ]
[ -n "$POSH_SESSION_ID" ]
builtin printf '\e]633;P;HasRichCommandDetection=True\a'
[[ -n "${bash_preexec_imported:-}" ]]
__vsc_dbg_trap="$(__vsc_get_trap DEBUG)"
[[ -z "$__vsc_dbg_trap" ]]
[[ "$__vsc_dbg_trap" != '__vsc_preexec "$_"' && "$__vsc_dbg_trap" != '__vsc_preexec_all "$_"' ]]
trap '__vsc_preexec_all "$_"' DEBUG
__vsc_original_prompt_command=${PROMPT_COMMAND:-}
echo 'Terminal capability test'
builtin unset VSCODE_INJECTION
[ -z "$VSCODE_SHELL_INTEGRATION" ]
[ -n "${VSCODE_ENV_REPLACE:-}" ]
[ -n "${VSCODE_ENV_PREPEND:-}" ]
[ -n "${VSCODE_ENV_APPEND:-}" ]
__vsc_regex_environment="^CYGWIN*|MINGW*|MSYS*"
[[ "$(uname -s)" =~ $__vsc_regex_environment ]]
__vsc_is_windows=0
__vsc_regex_histcontrol=".*(erasedups|ignoreboth|ignoredups).*"
[[ "$HISTCONTROL" =~ $__vsc_regex_histcontrol ]]
__vsc_history_verify=1
builtin unset __vsc_regex_environment
builtin unset __vsc_regex_histcontrol
__vsc_initialized=0
__vsc_original_PS1="$PS1"
__vsc_original_PS2="$PS2"
__vsc_custom_PS1=""
__vsc_custom_PS2=""
__vsc_in_command_execution="1"
__vsc_current_command=""
__vsc_nonce="$VSCODE_NONCE"
unset VSCODE_NONCE
__vsc_stable="$VSCODE_STABLE"
unset VSCODE_STABLE
[ "$__vsc_stable" = "0" ]
[ -n "$STARSHIP_SESSION_KEY" ]
[ -n "$POSH_SESSION_ID" ]
builtin printf '\e]633;P;HasRichCommandDetection=True\a'
[[ -n "${bash_preexec_imported:-}" ]]
__vsc_dbg_trap="$(__vsc_get_trap DEBUG)"
[[ -z "$__vsc_dbg_trap" ]]
[[ "$__vsc_dbg_trap" != '__vsc_preexec "$_"' && "$__vsc_dbg_trap" != '__vsc_preexec_all "$_"' ]]
trap '__vsc_preexec_all "$_"' DEBUG
__vsc_original_prompt_command=${PROMPT_COMMAND:-}
builtin unset VSCODE_INJECTION
[ -z "$VSCODE_SHELL_INTEGRATION" ]
[ -n "${VSCODE_ENV_REPLACE:-}" ]
[ -n "${VSCODE_ENV_PREPEND:-}" ]
[ -n "${VSCODE_ENV_APPEND:-}" ]
__vsc_regex_environment="^CYGWIN*|MINGW*|MSYS*"
[[ "$(uname -s)" =~ $__vsc_regex_environment ]]
__vsc_is_windows=0
__vsc_regex_histcontrol=".*(erasedups|ignoreboth|ignoredups).*"
[[ "$HISTCONTROL" =~ $__vsc_regex_histcontrol ]]
__vsc_history_verify=1
builtin unset __vsc_regex_environment
builtin unset __vsc_regex_histcontrol
__vsc_initialized=0
__vsc_original_PS1="$PS1"
__vsc_original_PS2="$PS2"
__vsc_custom_PS1=""
__vsc_custom_PS2=""
__vsc_in_command_execution="1"
__vsc_current_command=""
__vsc_nonce="$VSCODE_NONCE"
unset VSCODE_NONCE
__vsc_stable="$VSCODE_STABLE"
unset VSCODE_STABLE
[ "$__vsc_stable" = "0" ]
[ -n "$STARSHIP_SESSION_KEY" ]
[ -n "$POSH_SESSION_ID" ]
builtin printf '\e]633;P;HasRichCommandDetection=True\a'
[[ -n "${bash_preexec_imported:-}" ]]
__vsc_dbg_trap="$(__vsc_get_trap DEBUG)"
[[ -z "$__vsc_dbg_trap" ]]
[[ "$__vsc_dbg_trap" != '__vsc_preexec "$_"' && "$__vsc_dbg_trap" != '__vsc_preexec_all "$_"' ]]
trap '__vsc_preexec_all "$_"' DEBUG
__vsc_original_prompt_command=${PROMPT_COMMAND:-}
echo 'Terminal capability test'
builtin unset VSCODE_INJECTION
[ -z "$VSCODE_SHELL_INTEGRATION" ]
[ -n "${VSCODE_ENV_REPLACE:-}" ]
[ -n "${VSCODE_ENV_PREPEND:-}" ]
[ -n "${VSCODE_ENV_APPEND:-}" ]
__vsc_regex_environment="^CYGWIN*|MINGW*|MSYS*"
[[ "$(uname -s)" =~ $__vsc_regex_environment ]]
__vsc_is_windows=0
__vsc_regex_histcontrol=".*(erasedups|ignoreboth|ignoredups).*"
[[ "$HISTCONTROL" =~ $__vsc_regex_histcontrol ]]
__vsc_history_verify=1
builtin unset __vsc_regex_environment
builtin unset __vsc_regex_histcontrol
__vsc_initialized=0
__vsc_original_PS1="$PS1"
__vsc_original_PS2="$PS2"
__vsc_custom_PS1=""
__vsc_custom_PS2=""
__vsc_in_command_execution="1"
__vsc_current_command=""
__vsc_nonce="$VSCODE_NONCE"
unset VSCODE_NONCE
__vsc_stable="$VSCODE_STABLE"
unset VSCODE_STABLE
[ "$__vsc_stable" = "0" ]
[ -n "$STARSHIP_SESSION_KEY" ]
[ -n "$POSH_SESSION_ID" ]
builtin printf '\e]633;P;HasRichCommandDetection=True\a'
[[ -n "${bash_preexec_imported:-}" ]]
__vsc_dbg_trap="$(__vsc_get_trap DEBUG)"
[[ -z "$__vsc_dbg_trap" ]]
[[ "$__vsc_dbg_trap" != '__vsc_preexec "$_"' && "$__vsc_dbg_trap" != '__vsc_preexec_all "$_"' ]]
trap '__vsc_preexec_all "$_"' DEBUG
__vsc_original_prompt_command=${PROMPT_COMMAND:-}
mkdir -p number_guessing_game
__vsc_prompt_cmd_original
cd number_guessing_game
__vsc_prompt_cmd_original
chmod +x number_guess.sh
__vsc_prompt_cmd_original
git init
__vsc_prompt_cmd_original
git branch -m main
__vsc_prompt_cmd_original
git add number_guess.sh
__vsc_prompt_cmd_original
git commit -m "Initial commit"
__vsc_prompt_cmd_original
psql --username=freecodecamp --dbname=postgres -c "CREATE DATABASE number_guess;"
__vsc_prompt_cmd_original
psql --username=freecodecamp --dbname=number_guess -c " CREATE TABLE users ( user_id SERIAL PRIMARY KEY, username VARCHAR(22) UNIQUE NOT NULL ); CREATE TABLE games ( game_id SERIAL PRIMARY KEY, user_id INT REFERENCES users(user_id), secret_number INT NOT NULL, guesses INT NOT NULL );"
__vsc_prompt_cmd_original
git add number_guess.sh
__vsc_prompt_cmd_original
git commit -m "feat: implement complete number guessing game with database integration"
__vsc_prompt_cmd_original
echo "testuser"
builtin unset VSCODE_INJECTION
[ -z "$VSCODE_SHELL_INTEGRATION" ]
[ -n "${VSCODE_ENV_REPLACE:-}" ]
[ -n "${VSCODE_ENV_PREPEND:-}" ]
[ -n "${VSCODE_ENV_APPEND:-}" ]
__vsc_regex_environment="^CYGWIN*|MINGW*|MSYS*"
[[ "$(uname -s)" =~ $__vsc_regex_environment ]]
__vsc_is_windows=0
__vsc_regex_histcontrol=".*(erasedups|ignoreboth|ignoredups).*"
[[ "$HISTCONTROL" =~ $__vsc_regex_histcontrol ]]
__vsc_history_verify=1
builtin unset __vsc_regex_environment
builtin unset __vsc_regex_histcontrol
__vsc_initialized=0
__vsc_original_PS1="$PS1"
__vsc_original_PS2="$PS2"
__vsc_custom_PS1=""
__vsc_custom_PS2=""
__vsc_in_command_execution="1"
__vsc_current_command=""
__vsc_nonce="$VSCODE_NONCE"
unset VSCODE_NONCE
__vsc_stable="$VSCODE_STABLE"
unset VSCODE_STABLE
[ "$__vsc_stable" = "0" ]
[ -n "$STARSHIP_SESSION_KEY" ]
[ -n "$POSH_SESSION_ID" ]
builtin printf '\e]633;P;HasRichCommandDetection=True\a'
[[ -n "${bash_preexec_imported:-}" ]]
__vsc_dbg_trap="$(__vsc_get_trap DEBUG)"
[[ -z "$__vsc_dbg_trap" ]]
[[ "$__vsc_dbg_trap" != '__vsc_preexec "$_"' && "$__vsc_dbg_trap" != '__vsc_preexec_all "$_"' ]]
trap '__vsc_preexec_all "$_"' DEBUG
__vsc_original_prompt_command=${PROMPT_COMMAND:-}
./number_guess.sh
__vsc_prompt_cmd_original
pwd
builtin unset VSCODE_INJECTION
[ -z "$VSCODE_SHELL_INTEGRATION" ]
[ -n "${VSCODE_ENV_REPLACE:-}" ]
[ -n "${VSCODE_ENV_PREPEND:-}" ]
[ -n "${VSCODE_ENV_APPEND:-}" ]
__vsc_regex_environment="^CYGWIN*|MINGW*|MSYS*"
[[ "$(uname -s)" =~ $__vsc_regex_environment ]]
__vsc_is_windows=0
__vsc_regex_histcontrol=".*(erasedups|ignoreboth|ignoredups).*"
[[ "$HISTCONTROL" =~ $__vsc_regex_histcontrol ]]
__vsc_history_verify=1
builtin unset __vsc_regex_environment
builtin unset __vsc_regex_histcontrol
__vsc_initialized=0
__vsc_original_PS1="$PS1"
__vsc_original_PS2="$PS2"
__vsc_custom_PS1=""
__vsc_custom_PS2=""
__vsc_in_command_execution="1"
__vsc_current_command=""
__vsc_nonce="$VSCODE_NONCE"
unset VSCODE_NONCE
__vsc_stable="$VSCODE_STABLE"
unset VSCODE_STABLE
[ "$__vsc_stable" = "0" ]
[ -n "$STARSHIP_SESSION_KEY" ]
[ -n "$POSH_SESSION_ID" ]
builtin printf '\e]633;P;HasRichCommandDetection=True\a'
[[ -n "${bash_preexec_imported:-}" ]]
__vsc_dbg_trap="$(__vsc_get_trap DEBUG)"
[[ -z "$__vsc_dbg_trap" ]]
[[ "$__vsc_dbg_trap" != '__vsc_preexec "$_"' && "$__vsc_dbg_trap" != '__vsc_preexec_all "$_"' ]]
trap '__vsc_preexec_all "$_"' DEBUG
__vsc_original_prompt_command=${PROMPT_COMMAND:-}
./number_guess.sh
builtin unset VSCODE_INJECTION
[ -z "$VSCODE_SHELL_INTEGRATION" ]
[ -n "${VSCODE_ENV_REPLACE:-}" ]
[ -n "${VSCODE_ENV_PREPEND:-}" ]
[ -n "${VSCODE_ENV_APPEND:-}" ]
__vsc_regex_environment="^CYGWIN*|MINGW*|MSYS*"
[[ "$(uname -s)" =~ $__vsc_regex_environment ]]
__vsc_is_windows=0
__vsc_regex_histcontrol=".*(erasedups|ignoreboth|ignoredups).*"
[[ "$HISTCONTROL" =~ $__vsc_regex_histcontrol ]]
__vsc_history_verify=1
builtin unset __vsc_regex_environment
builtin unset __vsc_regex_histcontrol
__vsc_initialized=0
__vsc_original_PS1="$PS1"
__vsc_original_PS2="$PS2"
__vsc_custom_PS1=""
__vsc_custom_PS2=""
__vsc_in_command_execution="1"
__vsc_current_command=""
__vsc_nonce="$VSCODE_NONCE"
unset VSCODE_NONCE
__vsc_stable="$VSCODE_STABLE"
unset VSCODE_STABLE
[ "$__vsc_stable" = "0" ]
[ -n "$STARSHIP_SESSION_KEY" ]
[ -n "$POSH_SESSION_ID" ]
builtin printf '\e]633;P;HasRichCommandDetection=True\a'
[[ -n "${bash_preexec_imported:-}" ]]
__vsc_dbg_trap="$(__vsc_get_trap DEBUG)"
[[ -z "$__vsc_dbg_trap" ]]
[[ "$__vsc_dbg_trap" != '__vsc_preexec "$_"' && "$__vsc_dbg_trap" != '__vsc_preexec_all "$_"' ]]
trap '__vsc_preexec_all "$_"' DEBUG
__vsc_original_prompt_command=${PROMPT_COMMAND:-}
bash number_guess.sh
__vsc_prompt_cmd_original
file number_guess.sh
builtin unset VSCODE_INJECTION
[ -z "$VSCODE_SHELL_INTEGRATION" ]
[ -n "${VSCODE_ENV_REPLACE:-}" ]
[ -n "${VSCODE_ENV_PREPEND:-}" ]
[ -n "${VSCODE_ENV_APPEND:-}" ]
__vsc_regex_environment="^CYGWIN*|MINGW*|MSYS*"
[[ "$(uname -s)" =~ $__vsc_regex_environment ]]
__vsc_is_windows=0
__vsc_regex_histcontrol=".*(erasedups|ignoreboth|ignoredups).*"
[[ "$HISTCONTROL" =~ $__vsc_regex_histcontrol ]]
__vsc_history_verify=1
builtin unset __vsc_regex_environment
builtin unset __vsc_regex_histcontrol
__vsc_initialized=0
__vsc_original_PS1="$PS1"
__vsc_original_PS2="$PS2"
__vsc_custom_PS1=""
__vsc_custom_PS2=""
__vsc_in_command_execution="1"
__vsc_current_command=""
__vsc_nonce="$VSCODE_NONCE"
unset VSCODE_NONCE
__vsc_stable="$VSCODE_STABLE"
unset VSCODE_STABLE
[ "$__vsc_stable" = "0" ]
[ -n "$STARSHIP_SESSION_KEY" ]
[ -n "$POSH_SESSION_ID" ]
builtin printf '\e]633;P;HasRichCommandDetection=True\a'
[[ -n "${bash_preexec_imported:-}" ]]
__vsc_dbg_trap="$(__vsc_get_trap DEBUG)"
[[ -z "$__vsc_dbg_trap" ]]
[[ "$__vsc_dbg_trap" != '__vsc_preexec "$_"' && "$__vsc_dbg_trap" != '__vsc_preexec_all "$_"' ]]
trap '__vsc_preexec_all "$_"' DEBUG
__vsc_original_prompt_command=${PROMPT_COMMAND:-}
bash ./number_guess.sh
builtin unset VSCODE_INJECTION
[ -z "$VSCODE_SHELL_INTEGRATION" ]
[ -n "${VSCODE_ENV_REPLACE:-}" ]
[ -n "${VSCODE_ENV_PREPEND:-}" ]
[ -n "${VSCODE_ENV_APPEND:-}" ]
__vsc_regex_environment="^CYGWIN*|MINGW*|MSYS*"
[[ "$(uname -s)" =~ $__vsc_regex_environment ]]
__vsc_is_windows=0
__vsc_regex_histcontrol=".*(erasedups|ignoreboth|ignoredups).*"
[[ "$HISTCONTROL" =~ $__vsc_regex_histcontrol ]]
__vsc_history_verify=1
builtin unset __vsc_regex_environment
builtin unset __vsc_regex_histcontrol
__vsc_initialized=0
__vsc_original_PS1="$PS1"
__vsc_original_PS2="$PS2"
__vsc_custom_PS1=""
__vsc_custom_PS2=""
__vsc_in_command_execution="1"
__vsc_current_command=""
__vsc_nonce="$VSCODE_NONCE"
unset VSCODE_NONCE
__vsc_stable="$VSCODE_STABLE"
unset VSCODE_STABLE
[ "$__vsc_stable" = "0" ]
[ -n "$STARSHIP_SESSION_KEY" ]
[ -n "$POSH_SESSION_ID" ]
builtin printf '\e]633;P;HasRichCommandDetection=True\a'
[[ -n "${bash_preexec_imported:-}" ]]
__vsc_dbg_trap="$(__vsc_get_trap DEBUG)"
[[ -z "$__vsc_dbg_trap" ]]
[[ "$__vsc_dbg_trap" != '__vsc_preexec "$_"' && "$__vsc_dbg_trap" != '__vsc_preexec_all "$_"' ]]
trap '__vsc_preexec_all "$_"' DEBUG
__vsc_original_prompt_command=${PROMPT_COMMAND:-}
cat number_guess.sh
__vsc_prompt_cmd_original
pwd
builtin unset VSCODE_INJECTION
[ -z "$VSCODE_SHELL_INTEGRATION" ]
[ -n "${VSCODE_ENV_REPLACE:-}" ]
[ -n "${VSCODE_ENV_PREPEND:-}" ]
[ -n "${VSCODE_ENV_APPEND:-}" ]
__vsc_regex_environment="^CYGWIN*|MINGW*|MSYS*"
[[ "$(uname -s)" =~ $__vsc_regex_environment ]]
__vsc_is_windows=0
__vsc_regex_histcontrol=".*(erasedups|ignoreboth|ignoredups).*"
[[ "$HISTCONTROL" =~ $__vsc_regex_histcontrol ]]
__vsc_history_verify=1
builtin unset __vsc_regex_environment
builtin unset __vsc_regex_histcontrol
__vsc_initialized=0
__vsc_original_PS1="$PS1"
__vsc_original_PS2="$PS2"
__vsc_custom_PS1=""
__vsc_custom_PS2=""
__vsc_in_command_execution="1"
__vsc_current_command=""
__vsc_nonce="$VSCODE_NONCE"
unset VSCODE_NONCE
__vsc_stable="$VSCODE_STABLE"
unset VSCODE_STABLE
[ "$__vsc_stable" = "0" ]
[ -n "$STARSHIP_SESSION_KEY" ]
[ -n "$POSH_SESSION_ID" ]
builtin printf '\e]633;P;HasRichCommandDetection=True\a'
[[ -n "${bash_preexec_imported:-}" ]]
__vsc_dbg_trap="$(__vsc_get_trap DEBUG)"
[[ -z "$__vsc_dbg_trap" ]]
[[ "$__vsc_dbg_trap" != '__vsc_preexec "$_"' && "$__vsc_dbg_trap" != '__vsc_preexec_all "$_"' ]]
trap '__vsc_preexec_all "$_"' DEBUG
__vsc_original_prompt_command=${PROMPT_COMMAND:-}
/usr/bin/bash /workspace/project/number_guessing_game/number_guess.sh
__vsc_prompt_cmd_original
git add number_guess.sh
__vsc_prompt_cmd_original
psql --username=freecodecamp --dbname=number_guess -c "SELECT number_guess.sh FROM users;"
__vsc_prompt_cmd_original
psql --username=freecodecamp --dbname=number_guess -c "SELECT number_guess.sh FROM games;"
__vsc_prompt_cmd_original
chmod +x test_script.sh
__vsc_prompt_cmd_original
bash test_script.sh
__vsc_prompt_cmd_original
git log --oneline
__vsc_prompt_cmd_original
git add number_guess.sh
__vsc_prompt_cmd_original
git add number_guess.sh
__vsc_prompt_cmd_original
git log --oneline
__vsc_prompt_cmd_original
git status
__vsc_prompt_cmd_original
psql --username=freecodecamp --dbname=number_guess -c "SELECT COUNT(*) as total_users FROM users; SELECT COUNT(*) as total_games FROM games;"
__vsc_prompt_cmd_original
psql --username=freecodecamp --dbname=number_guess -c "\d users; \d games;"
__vsc_prompt_cmd_original
git log --oneline
builtin unset VSCODE_INJECTION
[ -z "$VSCODE_SHELL_INTEGRATION" ]
[ -n "${VSCODE_ENV_REPLACE:-}" ]
[ -n "${VSCODE_ENV_PREPEND:-}" ]
[ -n "${VSCODE_ENV_APPEND:-}" ]
__vsc_regex_environment="^CYGWIN*|MINGW*|MSYS*"
[[ "$(uname -s)" =~ $__vsc_regex_environment ]]
__vsc_is_windows=0
__vsc_regex_histcontrol=".*(erasedups|ignoreboth|ignoredups).*"
[[ "$HISTCONTROL" =~ $__vsc_regex_histcontrol ]]
__vsc_history_verify=1
builtin unset __vsc_regex_environment
builtin unset __vsc_regex_histcontrol
__vsc_initialized=0
__vsc_original_PS1="$PS1"
__vsc_original_PS2="$PS2"
__vsc_custom_PS1=""
__vsc_custom_PS2=""
__vsc_in_command_execution="1"
__vsc_current_command=""
__vsc_nonce="$VSCODE_NONCE"
unset VSCODE_NONCE
__vsc_stable="$VSCODE_STABLE"
unset VSCODE_STABLE
[ "$__vsc_stable" = "0" ]
[ -n "$STARSHIP_SESSION_KEY" ]
[ -n "$POSH_SESSION_ID" ]
builtin printf '\e]633;P;HasRichCommandDetection=True\a'
[[ -n "${bash_preexec_imported:-}" ]]
__vsc_dbg_trap="$(__vsc_get_trap DEBUG)"
[[ -z "$__vsc_dbg_trap" ]]
[[ "$__vsc_dbg_trap" != '__vsc_preexec "$_"' && "$__vsc_dbg_trap" != '__vsc_preexec_all "$_"' ]]
trap '__vsc_preexec_all "$_"' DEBUG
__vsc_original_prompt_command=${PROMPT_COMMAND:-}
git rebase -i HEAD~4
builtin unset VSCODE_INJECTION
[ -z "$VSCODE_SHELL_INTEGRATION" ]
[ -n "${VSCODE_ENV_REPLACE:-}" ]
[ -n "${VSCODE_ENV_PREPEND:-}" ]
[ -n "${VSCODE_ENV_APPEND:-}" ]
__vsc_regex_environment="^CYGWIN*|MINGW*|MSYS*"
[[ "$(uname -s)" =~ $__vsc_regex_environment ]]
__vsc_is_windows=0
__vsc_regex_histcontrol=".*(erasedups|ignoreboth|ignoredups).*"
[[ "$HISTCONTROL" =~ $__vsc_regex_histcontrol ]]
__vsc_history_verify=1
builtin unset __vsc_regex_environment
builtin unset __vsc_regex_histcontrol
__vsc_initialized=0
__vsc_original_PS1="$PS1"
__vsc_original_PS2="$PS2"
__vsc_custom_PS1=""
__vsc_custom_PS2=""
__vsc_in_command_execution="1"
__vsc_current_command=""
__vsc_nonce="$VSCODE_NONCE"
unset VSCODE_NONCE
__vsc_stable="$VSCODE_STABLE"
unset VSCODE_STABLE
[ "$__vsc_stable" = "0" ]
[ -n "$STARSHIP_SESSION_KEY" ]
[ -n "$POSH_SESSION_ID" ]
builtin printf '\e]633;P;HasRichCommandDetection=True\a'
[[ -n "${bash_preexec_imported:-}" ]]
__vsc_dbg_trap="$(__vsc_get_trap DEBUG)"
[[ -z "$__vsc_dbg_trap" ]]
[[ "$__vsc_dbg_trap" != '__vsc_preexec "$_"' && "$__vsc_dbg_trap" != '__vsc_preexec_all "$_"' ]]
trap '__vsc_preexec_all "$_"' DEBUG
__vsc_original_prompt_command=${PROMPT_COMMAND:-}
git rebase -i HEAD~2
__vsc_prompt_cmd_original
git status
__vsc_prompt_cmd_original
git reset --soft HEAD~2
__vsc_prompt_cmd_original
git status
__vsc_prompt_cmd_original
git commit -m "chore: add descriptive comment to script header"
__vsc_prompt_cmd_original
git log --oneline
__vsc_prompt_cmd_original
git add number_guess.sh
__vsc_prompt_cmd_original
git log --oneline
__vsc_prompt_cmd_original
git status
clear
__vsc_prompt_cmd_original
git remote add origin https://github.com/bniladridas/number-guessing-game.git
git status
__vsc_prompt_cmd_original
git remote -v
__vsc_prompt_cmd_original
git push -u origin main
__vsc_prompt_cmd_original
git log --oneline
