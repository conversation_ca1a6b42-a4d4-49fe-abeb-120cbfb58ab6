id: 'freeCodeCamp/learn-number-guessing-game:v1.0.0'
version: '2.0.0'
config:
  setup:
    commands:
      - ./.freeCodeCamp/setup.sh
      - cd .freeCodeCamp && npm install
  testRunner:
    command: npm run programmatic-test
    args:
      filter: --grep
      tap: --reporter=mocha-tap-reporter
    directory: .freeCodeCamp
  repo:
    uri: https://github.com/freeCodeCamp/learn-number-guessing-game
    branch: v2.0.0
  continue:
    commands:
      - './.freeCodeCamp/setup.sh'
  reset:
    commands:
      - './.freeCodeCamp/setup.sh'
  dependencies:
    - name: node
      version: '>=10'
  webhook:
    url: 'https://api.freecodecamp.org/coderoad-challenge-completed'
    events:
      init: false
      reset: false
      step_complete: false
      level_complete: false
      tutorial_complete: true
levels:
  - id: '1'
    steps:
      - id: '1.1'
